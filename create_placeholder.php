<?php
// Create placeholder image for the website
// Run this file once to generate the placeholder image

// Create directories if they don't exist
$uploadDir = 'uploads/images/';
$assetsDir = 'assets/images/';

if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

if (!is_dir($assetsDir)) {
    mkdir($assetsDir, 0755, true);
}

// Create placeholder image
$width = 800;
$height = 600;

// Create image
$image = imagecreate($width, $height);

// Define colors
$backgroundColor = imagecolorallocate($image, 240, 240, 240); // Light gray
$textColor = imagecolorallocate($image, 100, 100, 100); // Dark gray
$borderColor = imagecolorallocate($image, 200, 200, 200); // Medium gray

// Fill background
imagefill($image, 0, 0, $backgroundColor);

// Draw border
imagerectangle($image, 0, 0, $width-1, $height-1, $borderColor);
imagerectangle($image, 1, 1, $width-2, $height-2, $borderColor);

// Add text
$text = 'صورة غير متوفرة';
$fontSize = 5;

// Calculate text position (center)
$textWidth = imagefontwidth($fontSize) * strlen($text);
$textHeight = imagefontheight($fontSize);
$x = ($width - $textWidth) / 2;
$y = ($height - $textHeight) / 2;

// Add text
imagestring($image, $fontSize, $x, $y, $text, $textColor);

// Add dimensions text
$dimensionsText = $width . ' x ' . $height;
$dimX = ($width - imagefontwidth(3) * strlen($dimensionsText)) / 2;
$dimY = $y + 30;
imagestring($image, 3, $dimX, $dimY, $dimensionsText, $textColor);

// Save image
$placeholderPath = $assetsDir . 'placeholder.jpg';
imagejpeg($image, $placeholderPath, 80);

// Clean up
imagedestroy($image);

// Create favicon
$faviconSize = 32;
$favicon = imagecreate($faviconSize, $faviconSize);

// Define colors for favicon
$favBg = imagecolorallocate($favicon, 30, 64, 175); // Primary blue
$favText = imagecolorallocate($favicon, 255, 255, 255); // White

// Fill background
imagefill($favicon, 0, 0, $favBg);

// Add text (N for news)
$favLetter = 'N';
$favX = ($faviconSize - imagefontwidth(4) * 1) / 2;
$favY = ($faviconSize - imagefontheight(4)) / 2;
imagestring($favicon, 4, $favX, $favY, $favLetter, $favText);

// Save favicon
$faviconPath = $assetsDir . 'favicon.ico';
imagepng($favicon, str_replace('.ico', '.png', $faviconPath));

// Clean up
imagedestroy($favicon);

echo "Placeholder images created successfully!\n";
echo "- Placeholder: " . $placeholderPath . "\n";
echo "- Favicon: " . str_replace('.ico', '.png', $faviconPath) . "\n";
echo "\nYou can now delete this file (create_placeholder.php) as it's no longer needed.\n";
?>
