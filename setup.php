<?php
/**
 * Simple Setup Script for Arabic News Website
 * This is a simplified version to avoid installation issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

$message = '';
$error = '';

// Check if setup is already completed
if (file_exists('config/setup_complete.txt')) {
    $message = 'Setup already completed! You can now use the website.';
} else {
    // Run setup
    try {
        // Include database functions
        require_once 'config/database.php';
        
        // Create database and tables
        $result = createDatabase();
        
        if ($result) {
            // Mark setup as complete
            file_put_contents('config/setup_complete.txt', date('Y-m-d H:i:s'));
            $message = 'Setup completed successfully!';
        } else {
            $error = 'Failed to create database and tables.';
        }
        
    } catch (Exception $e) {
        $error = 'Setup failed: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد الموقع الإخباري العربي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #059669;
            background: #d1fae5;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #059669;
        }
        .error {
            color: #dc2626;
            background: #fee2e2;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dc2626;
        }
        .btn {
            background: #1e40af;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        h1 {
            color: #1e40af;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗞️ الموقع الإخباري العربي</h1>
        
        <?php if ($message): ?>
            <div class="success">
                ✅ <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error">
                ❌ <?php echo $error; ?>
            </div>
        <?php endif; ?>
        
        <h2>معلومات النظام</h2>
        <ul>
            <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
            <li><strong>MySQL Extension:</strong> <?php echo extension_loaded('pdo_mysql') ? '✅ Available' : '❌ Missing'; ?></li>
            <li><strong>GD Extension:</strong> <?php echo extension_loaded('gd') ? '✅ Available' : '❌ Missing'; ?></li>
            <li><strong>mbstring Extension:</strong> <?php echo extension_loaded('mbstring') ? '✅ Available' : '❌ Missing'; ?></li>
        </ul>
        
        <h2>بيانات الدخول الافتراضية</h2>
        <ul>
            <li><strong>اسم المستخدم:</strong> admin</li>
            <li><strong>كلمة المرور:</strong> admin123</li>
            <li><strong>البريد الإلكتروني:</strong> <EMAIL></li>
        </ul>
        
        <h2>الخطوات التالية</h2>
        <?php if (file_exists('config/setup_complete.txt')): ?>
            <p>تم إعداد الموقع بنجاح! يمكنك الآن:</p>
            <a href="index.php" class="btn">🏠 زيارة الموقع الرئيسي</a>
            <a href="admin/" class="btn">⚙️ دخول لوحة التحكم</a>
        <?php else: ?>
            <p>يرجى إصلاح الأخطاء أعلاه ثم تحديث الصفحة.</p>
            <a href="setup.php" class="btn">🔄 إعادة المحاولة</a>
        <?php endif; ?>
        
        <hr>
        <h2>استكشاف الأخطاء</h2>
        <p>إذا واجهت مشاكل، جرب هذه الروابط:</p>
        <a href="debug.php" class="btn">🔍 صفحة التشخيص</a>
        <a href="test.php" class="btn">🧪 اختبار PHP</a>
        
        <h2>ملاحظات مهمة</h2>
        <ul>
            <li>تأكد من تشغيل خادم MySQL</li>
            <li>تأكد من صحة إعدادات قاعدة البيانات في config/database.php</li>
            <li>تأكد من صلاحيات الكتابة في مجلد uploads</li>
            <li>قم بحذف ملفات setup.php و debug.php و test.php بعد انتهاء الإعداد</li>
        </ul>
    </div>
</body>
</html>
