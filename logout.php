<?php
require_once 'config/config.php';

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // Remove token from database
    if (isLoggedIn()) {
        try {
            $stmt = $pdo->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
        } catch(PDOException $e) {
            // Handle error silently
        }
    }
}

// Destroy session
session_destroy();

// Redirect to home page
redirectTo(SITE_URL . '/');
?>
