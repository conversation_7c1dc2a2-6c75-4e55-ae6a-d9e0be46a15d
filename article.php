<?php
require_once 'config/config.php';

// Get article slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit();
}

// Get article details
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.slug as category_slug, u.username as author_name
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.slug = ? AND a.status = 'published'
    ");
    $stmt->execute([$slug]);
    $article = $stmt->fetch();
    
    if (!$article) {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit();
    }
    
    // Update view count
    $updateStmt = $pdo->prepare("UPDATE articles SET views = views + 1 WHERE id = ?");
    $updateStmt->execute([$article['id']]);
    
} catch(PDOException $e) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit();
}

// Get article tags
try {
    $stmt = $pdo->prepare("
        SELECT t.name, t.slug 
        FROM tags t 
        JOIN article_tags at ON t.id = at.tag_id 
        WHERE at.article_id = ?
    ");
    $stmt->execute([$article['id']]);
    $tags = $stmt->fetchAll();
} catch(PDOException $e) {
    $tags = [];
}

// Get related articles
try {
    $stmt = $pdo->prepare("
        SELECT a.*, c.name as category_name, c.slug as category_slug
        FROM articles a 
        LEFT JOIN categories c ON a.category_id = c.id 
        WHERE a.category_id = ? AND a.id != ? AND a.status = 'published'
        ORDER BY a.created_at DESC 
        LIMIT 4
    ");
    $stmt->execute([$article['category_id'], $article['id']]);
    $relatedArticles = $stmt->fetchAll();
} catch(PDOException $e) {
    $relatedArticles = [];
}

// Get comments
try {
    $stmt = $pdo->prepare("
        SELECT c.*, u.username 
        FROM comments c 
        LEFT JOIN users u ON c.user_id = u.id 
        WHERE c.article_id = ? AND c.status = 'approved' 
        ORDER BY c.created_at DESC
    ");
    $stmt->execute([$article['id']]);
    $comments = $stmt->fetchAll();
} catch(PDOException $e) {
    $comments = [];
}

// Handle comment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_comment'])) {
    $commentContent = sanitize($_POST['content']);
    $authorName = sanitize($_POST['author_name']);
    $authorEmail = sanitize($_POST['author_email']);
    
    if (!empty($commentContent)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO comments (article_id, user_id, author_name, author_email, content, status) 
                VALUES (?, ?, ?, ?, ?, 'pending')
            ");
            $userId = isLoggedIn() ? $_SESSION['user_id'] : null;
            $stmt->execute([$article['id'], $userId, $authorName, $authorEmail, $commentContent]);
            
            showAlert('تم إرسال تعليقك بنجاح وسيتم مراجعته قريباً', 'success');
            redirectTo($_SERVER['REQUEST_URI']);
        } catch(PDOException $e) {
            showAlert('حدث خطأ أثناء إرسال التعليق', 'error');
        }
    }
}

$pageTitle = $article['title'];
$pageDescription = $article['excerpt'] ?: truncateText(strip_tags($article['content']), 160);

include 'includes/header.php';
?>

<div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
        <!-- Article Content -->
        <article class="article-full" data-article-id="<?php echo $article['id']; ?>">
            <!-- Article Header -->
            <div class="article-header mb-4">
                <div class="article-category-wrapper mb-3">
                    <a href="<?php echo SITE_URL; ?>/category.php?slug=<?php echo $article['category_slug']; ?>" 
                       class="article-category">
                        <?php echo $article['category_name']; ?>
                    </a>
                    <?php if ($article['is_breaking']): ?>
                        <span class="breaking-badge">عاجل</span>
                    <?php endif; ?>
                </div>
                
                <h1 class="article-title-full"><?php echo htmlspecialchars($article['title']); ?></h1>
                
                <div class="article-meta-full">
                    <div class="meta-item">
                        <i class="fas fa-user"></i>
                        <span><?php echo $article['author_name']; ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span><?php echo date('d F Y', strtotime($article['created_at'])); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span><?php echo timeAgo($article['created_at']); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-eye"></i>
                        <span><?php echo formatNumber($article['views']); ?> مشاهدة</span>
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <?php if ($article['featured_image']): ?>
            <div class="article-image-wrapper mb-4">
                <img src="<?php echo SITE_URL . '/uploads/' . $article['featured_image']; ?>" 
                     alt="<?php echo htmlspecialchars($article['title']); ?>" 
                     class="article-image-full">
            </div>
            <?php endif; ?>

            <!-- Article Content -->
            <div class="article-content-full">
                <?php echo nl2br($article['content']); ?>
            </div>

            <!-- Article Tags -->
            <?php if (!empty($tags)): ?>
            <div class="article-tags mt-4">
                <h6>الكلمات المفتاحية:</h6>
                <div class="tags-list">
                    <?php foreach ($tags as $tag): ?>
                        <a href="<?php echo SITE_URL; ?>/tag.php?slug=<?php echo $tag['slug']; ?>" 
                           class="tag-link"><?php echo $tag['name']; ?></a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Social Sharing -->
            <div class="article-sharing mt-4">
                <h6>شارك المقال:</h6>
                <div class="sharing-buttons">
                    <button class="share-btn btn btn-facebook" 
                            data-platform="facebook" 
                            data-url="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>"
                            data-title="<?php echo htmlspecialchars($article['title']); ?>">
                        <i class="fab fa-facebook-f"></i>
                        فيسبوك
                    </button>
                    <button class="share-btn btn btn-twitter" 
                            data-platform="twitter" 
                            data-url="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>"
                            data-title="<?php echo htmlspecialchars($article['title']); ?>">
                        <i class="fab fa-twitter"></i>
                        تويتر
                    </button>
                    <button class="share-btn btn btn-whatsapp" 
                            data-platform="whatsapp" 
                            data-url="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>"
                            data-title="<?php echo htmlspecialchars($article['title']); ?>">
                        <i class="fab fa-whatsapp"></i>
                        واتساب
                    </button>
                    <button class="share-btn btn btn-telegram" 
                            data-platform="telegram" 
                            data-url="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>"
                            data-title="<?php echo htmlspecialchars($article['title']); ?>">
                        <i class="fab fa-telegram"></i>
                        تلغرام
                    </button>
                    <button class="btn btn-copy" onclick="copyToClipboard('<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>')">
                        <i class="fas fa-copy"></i>
                        نسخ الرابط
                    </button>
                </div>
            </div>
        </article>

        <!-- Related Articles -->
        <?php if (!empty($relatedArticles)): ?>
        <div class="related-articles mt-5">
            <h4 class="section-title mb-4">
                <i class="fas fa-newspaper"></i>
                مقالات ذات صلة
            </h4>
            <div class="row">
                <?php foreach ($relatedArticles as $relatedArticle): ?>
                <div class="col-md-6 mb-3">
                    <div class="related-article-card">
                        <img src="<?php echo $relatedArticle['featured_image'] ? SITE_URL . '/uploads/' . $relatedArticle['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($relatedArticle['title']); ?>" 
                             class="related-article-image">
                        <div class="related-article-content">
                            <h6 class="related-article-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $relatedArticle['slug']; ?>">
                                    <?php echo htmlspecialchars($relatedArticle['title']); ?>
                                </a>
                            </h6>
                            <div class="related-article-meta">
                                <span><?php echo timeAgo($relatedArticle['created_at']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Comments Section -->
        <?php if (getSetting('comments_enabled', '1') == '1'): ?>
        <div class="comments-section mt-5">
            <h4 class="section-title mb-4">
                <i class="fas fa-comments"></i>
                التعليقات (<?php echo count($comments); ?>)
            </h4>

            <!-- Comment Form -->
            <div class="comment-form-wrapper mb-4">
                <form method="POST" class="comment-form" data-validate>
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <?php if (!isLoggedIn()): ?>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" name="author_name" class="form-control" 
                                   placeholder="الاسم *" required>
                        </div>
                        <div class="col-md-6">
                            <input type="email" name="author_email" class="form-control" 
                                   placeholder="البريد الإلكتروني *" required>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <textarea name="content" class="form-control" rows="4" 
                                  placeholder="اكتب تعليقك هنا..." required></textarea>
                    </div>
                    
                    <button type="submit" name="submit_comment" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        إرسال التعليق
                    </button>
                </form>
            </div>

            <!-- Comments List -->
            <div class="comments-list">
                <?php foreach ($comments as $comment): ?>
                <div class="comment-item">
                    <div class="comment-header">
                        <div class="comment-author">
                            <i class="fas fa-user-circle"></i>
                            <?php echo $comment['username'] ?: $comment['author_name']; ?>
                        </div>
                        <div class="comment-date">
                            <?php echo timeAgo($comment['created_at']); ?>
                        </div>
                    </div>
                    <div class="comment-content">
                        <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <?php if (empty($comments)): ?>
                <div class="no-comments">
                    <i class="fas fa-comments"></i>
                    <p>لا توجد تعليقات بعد. كن أول من يعلق!</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Popular Articles Widget -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-fire"></i>
                الأكثر قراءة
            </div>
            <div class="widget-content">
                <!-- Popular articles will be loaded here -->
            </div>
        </div>

        <!-- Categories Widget -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-list"></i>
                الأقسام
            </div>
            <div class="widget-content">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.article-title-full {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1.3;
    color: var(--text-color);
    margin-bottom: 20px;
}

.article-meta-full {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.breaking-badge {
    background: var(--secondary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 10px;
}

.article-image-full {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius);
}

.article-content-full {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
}

.article-content-full p {
    margin-bottom: 20px;
}

.article-tags h6 {
    color: var(--text-color);
    margin-bottom: 10px;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-link {
    background: var(--light-color);
    color: var(--text-color);
    padding: 6px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.tag-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.article-sharing h6 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.sharing-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.share-btn, .btn-copy {
    padding: 8px 16px;
    border: none;
    border-radius: var(--border-radius);
    color: white;
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.btn-facebook { background: #1877f2; }
.btn-twitter { background: #1da1f2; }
.btn-whatsapp { background: #25d366; }
.btn-telegram { background: #0088cc; }
.btn-copy { background: var(--text-light); }

.share-btn:hover, .btn-copy:hover {
    transform: translateY(-2px);
    opacity: 0.9;
}

.related-article-card {
    display: flex;
    gap: 15px;
    padding: 15px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.related-article-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.related-article-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
    flex-shrink: 0;
}

.related-article-title {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

.related-article-title a {
    color: var(--text-color);
    text-decoration: none;
}

.related-article-title a:hover {
    color: var(--primary-color);
}

.related-article-meta {
    font-size: 0.8rem;
    color: var(--text-light);
}

.comment-form-wrapper {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.comment-item {
    background: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 15px;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.comment-author {
    font-weight: 600;
    color: var(--text-color);
}

.comment-date {
    font-size: 0.9rem;
    color: var(--text-light);
}

.comment-content {
    line-height: 1.6;
    color: var(--text-color);
}

.no-comments {
    text-align: center;
    padding: 40px;
    color: var(--text-light);
}

.no-comments i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .article-title-full {
        font-size: 1.8rem;
    }
    
    .article-meta-full {
        flex-direction: column;
        gap: 10px;
    }
    
    .sharing-buttons {
        justify-content: center;
    }
    
    .related-article-card {
        flex-direction: column;
        text-align: center;
    }
    
    .related-article-image {
        width: 100%;
        height: 150px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
