/* Arabic News Website Styles */
:root {
    --primary-color: #1e40af;
    --secondary-color: #dc2626;
    --accent-color: #059669;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --border-color: #e5e7eb;
    --text-color: #374151;
    --text-light: #6b7280;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
    direction: rtl;
    text-align: right;
}

/* Breaking News Ticker */
.breaking-news-ticker {
    background: linear-gradient(135deg, var(--secondary-color), #ef4444);
    color: white;
    padding: 8px 0;
    overflow: hidden;
    position: relative;
}

.breaking-label {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
}

.ticker-content {
    overflow: hidden;
    white-space: nowrap;
}

.ticker-text {
    display: inline-block;
    animation: scroll-rtl 30s linear infinite;
    padding-right: 100%;
}

.ticker-text a {
    color: white;
    text-decoration: none;
    margin: 0 20px;
    transition: var(--transition);
}

.ticker-text a:hover {
    text-decoration: underline;
}

@keyframes scroll-rtl {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Header Styles */
.main-header {
    background: white;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.top-bar {
    background: var(--dark-color);
    color: white;
    padding: 8px 0;
    font-size: 0.9rem;
}

.date-time {
    display: flex;
    align-items: center;
    gap: 8px;
}

.social-links {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.social-link {
    color: white;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    text-decoration: none;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.header-main {
    padding: 20px 0;
}

.logo-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.logo-img {
    max-height: 60px;
    width: auto;
}

.search-form {
    max-width: 400px;
    margin: 0 auto;
}

.search-input {
    border: 2px solid var(--border-color);
    border-left: none;
    padding: 12px 16px;
    font-size: 1rem;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

.btn-search {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
    padding: 12px 20px;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    transition: var(--transition);
}

.btn-search:hover {
    background: #1d4ed8;
    border-color: #1d4ed8;
}

/* Navigation */
.main-navigation {
    background: var(--primary-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.navbar {
    padding: 0;
}

.navbar-toggler {
    border: none;
    color: white;
}

.nav-link {
    color: white !important;
    padding: 15px 20px !important;
    font-weight: 500;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.2);
}

/* Main Content */
.main-content {
    padding: 30px 0;
    min-height: calc(100vh - 400px);
}

/* Article Cards */
.article-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    height: 100%;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.article-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.article-content {
    padding: 20px;
}

.article-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.4;
}

.article-title a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.article-title a:hover {
    color: var(--primary-color);
}

.article-excerpt {
    color: var(--text-light);
    margin-bottom: 15px;
    line-height: 1.6;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-light);
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.article-category {
    background: var(--primary-color);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    text-decoration: none;
    transition: var(--transition);
}

.article-category:hover {
    background: #1d4ed8;
    color: white;
}

/* Featured Article */
.featured-article {
    position: relative;
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 30px;
}

.featured-article .article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 40px 30px 30px;
}

.featured-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.3;
}

.featured-title a {
    color: white;
    text-decoration: none;
}

/* Sidebar */
.sidebar-widget {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.widget-header {
    background: var(--primary-color);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
}

.widget-content {
    padding: 20px;
}

.widget-list {
    list-style: none;
}

.widget-list li {
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.widget-list li:last-child {
    border-bottom: none;
}

.widget-list a {
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.widget-list a:hover {
    color: var(--primary-color);
}

/* Match Schedule Widget */
.match-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    background: white;
    transition: var(--transition);
}

.match-item:hover {
    box-shadow: var(--shadow);
}

.match-teams {
    font-weight: 600;
}

.match-score {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.match-time {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Footer */
.main-footer {
    background: var(--dark-color);
    color: white;
    margin-top: 50px;
}

.footer-top {
    padding: 50px 0 30px;
}

.widget-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.widget-text {
    color: #d1d5db;
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 8px;
}

.footer-links a {
    color: #d1d5db;
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
}

.newsletter-form .input-group {
    margin-top: 15px;
}

.newsletter-form .form-control {
    border: 1px solid #4b5563;
    background: #374151;
    color: white;
}

.newsletter-form .form-control:focus {
    border-color: var(--primary-color);
    background: #374151;
    color: white;
    box-shadow: none;
}

.footer-bottom {
    border-top: 1px solid #4b5563;
    padding: 20px 0;
}

.copyright {
    margin: 0;
    color: #d1d5db;
}

.footer-menu a {
    color: #d1d5db;
    text-decoration: none;
    margin: 0 10px;
    transition: var(--transition);
}

.footer-menu a:hover {
    color: white;
}

.separator {
    color: #6b7280;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: #1d4ed8;
    transform: translateY(-3px);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-main {
        text-align: center;
    }
    
    .header-main .row > div {
        margin-bottom: 15px;
    }
    
    .search-form {
        max-width: 100%;
    }
    
    .featured-title {
        font-size: 1.4rem;
    }
    
    .featured-overlay {
        padding: 20px;
    }
    
    .article-card {
        margin-bottom: 20px;
    }
    
    .breaking-news-ticker {
        font-size: 0.9rem;
    }
    
    .ticker-text {
        animation-duration: 20s;
    }
}

@media (max-width: 576px) {
    .top-bar {
        text-align: center;
    }
    
    .date-time {
        justify-content: center;
        margin-bottom: 10px;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .logo-text {
        font-size: 1.5rem;
    }
    
    .nav-link {
        padding: 12px 15px !important;
    }
}

/* Print Styles */
@media print {
    .breaking-news-ticker,
    .main-navigation,
    .sidebar,
    .main-footer,
    .scroll-to-top {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
    }
    
    .article-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
