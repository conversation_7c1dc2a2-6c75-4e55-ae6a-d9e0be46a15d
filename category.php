<?php
require_once 'config/config.php';

// Get category slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit();
}

// Get category details
try {
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE slug = ?");
    $stmt->execute([$slug]);
    $category = $stmt->fetch();
    
    if (!$category) {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit();
    }
} catch(PDOException $e) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit();
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ARTICLES_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get articles in this category
try {
    // Count total articles
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM articles WHERE category_id = ? AND status = 'published'");
    $stmt->execute([$category['id']]);
    $totalArticles = $stmt->fetch()['total'];
    
    // Calculate pagination
    $totalPages = ceil($totalArticles / $limit);
    
    // Get articles for current page
    $stmt = $pdo->prepare("
        SELECT a.*, u.username as author_name
        FROM articles a 
        LEFT JOIN users u ON a.author_id = u.id 
        WHERE a.category_id = ? AND a.status = 'published'
        ORDER BY a.created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$category['id'], $limit, $offset]);
    $articles = $stmt->fetchAll();
    
} catch(PDOException $e) {
    $articles = [];
    $totalArticles = 0;
    $totalPages = 0;
}

$pageTitle = $category['name'];
$pageDescription = $category['description'] ?: "جميع أخبار " . $category['name'];

include 'includes/header.php';
?>

<div class="row">
    <!-- Main Content -->
    <div class="col-lg-8">
        <!-- Category Header -->
        <div class="category-header">
            <h1 class="category-title">
                <i class="fas fa-folder-open"></i>
                <?php echo htmlspecialchars($category['name']); ?>
            </h1>
            <?php if ($category['description']): ?>
                <p class="category-description"><?php echo htmlspecialchars($category['description']); ?></p>
            <?php endif; ?>
            <div class="category-meta">
                <span class="articles-count">
                    <i class="fas fa-newspaper"></i>
                    <?php echo formatNumber($totalArticles); ?> مقال
                </span>
            </div>
        </div>

        <!-- Articles List -->
        <?php if (!empty($articles)): ?>
            <div class="articles-grid">
                <?php foreach ($articles as $article): ?>
                    <div class="article-card">
                        <div class="article-image-wrapper">
                            <img src="<?php echo $article['featured_image'] ? SITE_URL . '/uploads/' . $article['featured_image'] : SITE_URL . '/assets/images/placeholder.jpg'; ?>" 
                                 alt="<?php echo htmlspecialchars($article['title']); ?>" 
                                 class="article-image">
                            <?php if ($article['is_breaking']): ?>
                                <span class="breaking-badge">عاجل</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="article-content">
                            <h3 class="article-title">
                                <a href="<?php echo SITE_URL; ?>/article.php?slug=<?php echo $article['slug']; ?>">
                                    <?php echo htmlspecialchars($article['title']); ?>
                                </a>
                            </h3>
                            
                            <p class="article-excerpt">
                                <?php echo truncateText($article['excerpt'] ?: strip_tags($article['content']), 150); ?>
                            </p>
                            
                            <div class="article-meta">
                                <div class="meta-left">
                                    <span class="author">
                                        <i class="fas fa-user"></i>
                                        <?php echo $article['author_name']; ?>
                                    </span>
                                    <span class="date">
                                        <i class="fas fa-clock"></i>
                                        <?php echo timeAgo($article['created_at']); ?>
                                    </span>
                                </div>
                                <div class="meta-right">
                                    <span class="views">
                                        <i class="fas fa-eye"></i>
                                        <?php echo formatNumber($article['views']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav class="pagination-nav" aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?slug=<?php echo $slug; ?>&page=<?php echo $page - 1; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                    السابق
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start = max(1, $page - 2);
                        $end = min($totalPages, $page + 2);
                        
                        if ($start > 1) {
                            echo '<li class="page-item"><a class="page-link" href="?slug=' . $slug . '&page=1">1</a></li>';
                            if ($start > 2) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                        }
                        
                        for ($i = $start; $i <= $end; $i++) {
                            $active = $i == $page ? 'active' : '';
                            echo '<li class="page-item ' . $active . '">
                                    <a class="page-link" href="?slug=' . $slug . '&page=' . $i . '">' . $i . '</a>
                                  </li>';
                        }
                        
                        if ($end < $totalPages) {
                            if ($end < $totalPages - 1) {
                                echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                            }
                            echo '<li class="page-item"><a class="page-link" href="?slug=' . $slug . '&page=' . $totalPages . '">' . $totalPages . '</a></li>';
                        }
                        ?>

                        <!-- Next Page -->
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?slug=<?php echo $slug; ?>&page=<?php echo $page + 1; ?>">
                                    التالي
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <!-- No Articles -->
            <div class="no-articles">
                <div class="no-articles-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h3>لا توجد مقالات في هذا القسم</h3>
                <p>لم يتم نشر أي مقالات في قسم "<?php echo htmlspecialchars($category['name']); ?>" بعد.</p>
                <a href="<?php echo SITE_URL; ?>/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة إلى الرئيسية
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Other Categories -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-list"></i>
                أقسام أخرى
            </div>
            <div class="widget-content">
                <ul class="categories-list">
                    <?php
                    try {
                        $stmt = $pdo->prepare("
                            SELECT c.*, COUNT(a.id) as article_count 
                            FROM categories c 
                            LEFT JOIN articles a ON c.id = a.category_id AND a.status = 'published'
                            WHERE c.id != ?
                            GROUP BY c.id 
                            ORDER BY c.name
                        ");
                        $stmt->execute([$category['id']]);
                        $otherCategories = $stmt->fetchAll();
                        
                        foreach ($otherCategories as $cat) {
                            echo '<li>
                                    <a href="' . SITE_URL . '/category.php?slug=' . $cat['slug'] . '">
                                        ' . $cat['name'] . '
                                        <span class="category-count">(' . $cat['article_count'] . ')</span>
                                    </a>
                                  </li>';
                        }
                    } catch(PDOException $e) {
                        echo '<li>حدث خطأ في تحميل الأقسام</li>';
                    }
                    ?>
                </ul>
            </div>
        </div>

        <!-- Popular Articles in Category -->
        <div class="sidebar-widget">
            <div class="widget-header">
                <i class="fas fa-fire"></i>
                الأكثر قراءة في <?php echo $category['name']; ?>
            </div>
            <div class="widget-content">
                <ul class="popular-articles">
                    <?php
                    try {
                        $stmt = $pdo->prepare("
                            SELECT title, slug, views, created_at
                            FROM articles 
                            WHERE category_id = ? AND status = 'published'
                            ORDER BY views DESC 
                            LIMIT 5
                        ");
                        $stmt->execute([$category['id']]);
                        $popularArticles = $stmt->fetchAll();
                        
                        foreach ($popularArticles as $index => $article) {
                            echo '<li class="popular-item">
                                    <div class="popular-rank">' . ($index + 1) . '</div>
                                    <div class="popular-content">
                                        <h6><a href="' . SITE_URL . '/article.php?slug=' . $article['slug'] . '">' . 
                                             truncateText($article['title'], 60) . '</a></h6>
                                        <div class="popular-meta">
                                            <span>' . formatNumber($article['views']) . ' مشاهدة</span>
                                            <span>' . timeAgo($article['created_at']) . '</span>
                                        </div>
                                    </div>
                                  </li>';
                        }
                        
                        if (empty($popularArticles)) {
                            echo '<li>لا توجد مقالات شائعة في هذا القسم</li>';
                        }
                    } catch(PDOException $e) {
                        echo '<li>حدث خطأ في تحميل المقالات الشائعة</li>';
                    }
                    ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.category-header {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    text-align: center;
}

.category-title {
    color: var(--primary-color);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.category-description {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 20px;
    line-height: 1.6;
}

.category-meta {
    color: var(--text-light);
    font-size: 1rem;
}

.articles-count {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.articles-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
}

.no-articles {
    text-align: center;
    padding: 60px 30px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.no-articles-icon {
    font-size: 4rem;
    color: var(--text-light);
    margin-bottom: 20px;
}

.no-articles h3 {
    color: var(--text-color);
    margin-bottom: 15px;
}

.no-articles p {
    color: var(--text-light);
    margin-bottom: 25px;
}

.categories-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.categories-list li {
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.categories-list li:last-child {
    border-bottom: none;
}

.categories-list a {
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.categories-list a:hover {
    color: var(--primary-color);
}

.category-count {
    color: var(--text-light);
    font-size: 0.9rem;
}

.popular-articles {
    list-style: none;
    margin: 0;
    padding: 0;
}

.popular-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.popular-item:last-child {
    border-bottom: none;
}

.popular-rank {
    background: var(--primary-color);
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    flex-shrink: 0;
}

.popular-content h6 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.popular-content a {
    color: var(--text-color);
    text-decoration: none;
}

.popular-content a:hover {
    color: var(--primary-color);
}

.popular-meta {
    font-size: 0.8rem;
    color: var(--text-light);
}

.popular-meta span {
    margin-left: 10px;
}

@media (max-width: 768px) {
    .category-title {
        font-size: 2rem;
    }
    
    .category-header {
        padding: 20px;
    }
    
    .articles-grid {
        gap: 20px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
