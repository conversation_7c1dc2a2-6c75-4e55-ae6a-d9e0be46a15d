<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : SITE_DESCRIPTION; ?>">
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($pageTitle) ? $pageTitle : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($pageDescription) ? $pageDescription : SITE_DESCRIPTION; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($pageTitle) ? $pageTitle : SITE_NAME; ?>">
    <meta name="twitter:description" content="<?php echo isset($pageDescription) ? $pageDescription : SITE_DESCRIPTION; ?>">
</head>
<body>
    <!-- Breaking News Ticker -->
    <?php if (getSetting('breaking_news_enabled', '1') == '1'): ?>
    <div class="breaking-news-ticker">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-auto">
                    <span class="breaking-label">
                        <i class="fas fa-bolt"></i>
                        عاجل
                    </span>
                </div>
                <div class="col">
                    <div class="ticker-content">
                        <div class="ticker-text" id="breakingNewsText">
                            <?php
                            // Get breaking news
                            try {
                                $stmt = $pdo->prepare("SELECT title, slug FROM articles WHERE is_breaking = 1 AND status = 'published' ORDER BY created_at DESC LIMIT 5");
                                $stmt->execute();
                                $breakingNews = $stmt->fetchAll();
                                
                                if ($breakingNews) {
                                    $newsItems = [];
                                    foreach ($breakingNews as $news) {
                                        $newsItems[] = '<a href="' . SITE_URL . '/article.php?slug=' . $news['slug'] . '">' . $news['title'] . '</a>';
                                    }
                                    echo implode(' • ', $newsItems);
                                } else {
                                    echo 'مرحباً بكم في ' . SITE_NAME . ' - موقعكم الإخباري الموثوق';
                                }
                            } catch(PDOException $e) {
                                echo 'مرحباً بكم في ' . SITE_NAME;
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Main Header -->
    <header class="main-header">
        <div class="container">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="date-time">
                            <i class="fas fa-calendar-alt"></i>
                            <span id="currentDate"><?php echo date('l, d F Y'); ?></span>
                            <span class="mx-2">|</span>
                            <i class="fas fa-clock"></i>
                            <span id="currentTime"><?php echo date('H:i'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="social-links text-end">
                            <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logo and Search -->
            <div class="header-main">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <div class="logo">
                            <a href="<?php echo SITE_URL; ?>">
                                <?php 
                                $siteLogo = getSetting('site_logo');
                                if ($siteLogo): ?>
                                    <img src="<?php echo SITE_URL . '/uploads/' . $siteLogo; ?>" alt="<?php echo SITE_NAME; ?>" class="logo-img">
                                <?php else: ?>
                                    <h1 class="logo-text"><?php echo SITE_NAME; ?></h1>
                                <?php endif; ?>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="search-box">
                            <form action="<?php echo SITE_URL; ?>/search.php" method="GET" class="search-form">
                                <div class="input-group">
                                    <input type="text" name="q" class="form-control search-input" 
                                           placeholder="ابحث في الأخبار..." 
                                           value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>">
                                    <button class="btn btn-search" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="header-actions text-end">
                            <?php if (isLoggedIn()): ?>
                                <div class="dropdown">
                                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user"></i>
                                        <?php echo $_SESSION['username']; ?>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <?php if (isEditor()): ?>
                                            <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/admin/"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                                        <?php endif; ?>
                                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/profile.php"><i class="fas fa-user-edit"></i> الملف الشخصي</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-outline-primary">
                                    <i class="fas fa-sign-in-alt"></i>
                                    تسجيل الدخول
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="main-navigation">
                <div class="navbar navbar-expand-lg">
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="mainNavbar">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo SITE_URL; ?>">
                                    <i class="fas fa-home"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <?php
                            // Get categories for navigation
                            try {
                                $stmt = $pdo->prepare("SELECT name, slug FROM categories ORDER BY name");
                                $stmt->execute();
                                $categories = $stmt->fetchAll();
                                
                                foreach ($categories as $category) {
                                    echo '<li class="nav-item">
                                            <a class="nav-link" href="' . SITE_URL . '/category.php?slug=' . $category['slug'] . '">
                                                ' . $category['name'] . '
                                            </a>
                                          </li>';
                                }
                            } catch(PDOException $e) {
                                // Handle error silently
                            }
                            ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo SITE_URL; ?>/matches.php">
                                    <i class="fas fa-futbol"></i>
                                    المباريات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo SITE_URL; ?>/contact.php">
                                    <i class="fas fa-envelope"></i>
                                    اتصل بنا
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <?php displayAlert(); ?>
